#cloud-config

# Cloud config examples at https://cloudinit.readthedocs.io/en/latest/topics/examples.html

users:
  - name: ubuntu
    sudo: ALL=(ALL) NOPASSWD:ALL
    ssh_authorized_keys:
      - ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABAQDfD2ORM6LzQC4Wfp/+MCb10FFtx83Gw2n5hFMm+iZFm94MmEOuYl/xKS6Eke2tHFU8s9x9FUGkYo/Nzi5oYFje0fez1Fu7vMwhja98opupCWU0RkuzesPnoY2CuEEBl7nFZh7avqvJFZzkcz2q7aisG/FKYjbzTlt2JH51watlzkGDOy9bYzqdSUVvuZ3chICvJA6Vf2UOtBEky44gQ+ckuoS5Ka7B+TcB1FTl9BQGnQTyfz4HGP65Avj+bkE+ei5p4KXxbMpTz65P5FvPuVQOUajPv12OS1q+5sHRtOjt4Hqr+dTvNwe5qUSdQSxGjg4/Z9nYwSfamMyL2+gMM9YR <EMAIL>
      - ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABAQDvQicdlNad+NgFEjzpmjh91ZlK6BHHx/+Xdajm9xE4Qjr1RcunQ9vJMDWMG549bsfDb3iNg1m5XxHtFSOgX3M9iadrlRnO6w+s83G6lVn4Ce54f2hxhZpLat8A9BAru/CDhA7egm1MCFnBxJFPmIOoeVIyZG0sHZ4wnzr6JDGdo/OyCcBs7KEvrIJK8aDfcC/FCM6bD7TXkPGPbfnl5MaOAm70j4QiyYZ2zcFPoXmjcF8vhYA//0qFnMx099ErtKhzJr+B+54TxKAO0ElQ7V65YkwN5ZOgFiWQxQlyjeVHX7j12Jfifx1kfmgaQpV8Nw2C/S96TctY/T2fFSH38gjZ
      - ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAACAQCRa5Dfs6+l9c4KxJDHHqj3QQ8IDGuFn1+FKg3QhL2iC3jU04Vb9NkMlkXmvTdnWy7SySwSaCGlj3iwZsLD9KBznmnvDZ1st6WLxcGIJCN6rKld3JV4nQnO6J8rP3WbjLprRAUvVgZbiBhm9st7fOYkjBAdsLx9IwjhHlVHFoHVNwMrS4QYVjkHRFVHBuMYSWpCNt1TtM7wuVaQBk756UlQ0SqBoo3d4DHbgOnNrUMb5U8laZq10gQ+0RFKCUIoBVCkoQazAHlpdAT/L7ANeSHniQ4Zi9epwSsjyv56FuXW2Ou59JJSvpCUPmqazTCyRFEUc7AAVZytqqDBZuTZfReWZ1EC6NNYemwqQ6HnvtS9VkoPsIeUj4SSMxFYaLvXcUymwQgmj3kHDxBDTCtT1VRecicyiZQvmgSJ+iFlcZb6br/S6kT9EWCY1WPs9dvHyf5392xa/CuEPapOGj90nR4g5jOKdSJtYOiAwJsWSQ3uqU2GpFsMqIWTtZJi4nzN9+RmLh6Yp5RGWG6GRf+P0YWJFr8G+o+q7IjrZuD6WFKRlXcF4zYjMSF/YWGw0Lmzz242Gq1ArAlwtK3nrP0Vb/mHOVLgTFZwoH/LsGH3Av26L6V+Kl1Ist1P7ovDrNb/tthxKoAOxNyMuNxDel4Nx1xPiS/9ccuJFd56vSmnAchgIQ== deployment-server
      - ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABgQDCC8+e/lV++xqets2fX5U6Uw9/FM/mdjt7dP7I0uMk6Csl4IEA4gzvZqORy59nSm3JD4gvClEK//iDKBx6Odmx/Go2XkBIvv8c69YUsQ/niT3O1VRM1A7RDYeeY5EKTYO9IJJsOwG2/CPrxj75g1gXVOuNmBrxb/wDuV6eMs5IkQ7aPj0QXoF/qfdWxFkL3cpa3tYBTyUB/eTpZR3i5fWlNFLdH9xL8lWsmILi7PRtDzaMq0KZz3o3TpAliZZjrQPnTW1aDHXKqP8+l8630/hI67G3EkniM9aoZmVcxUZSKxYZMCD9uNSKcKRKdEPC+hElTFIZl3uHbfV/BoXSBbWSw3t3NjRhvb7F5kBCTLL40hGtE7GThEIYaMrJINkRkRtA5nJlvge8Zc8gcjuY4nSVf6OSvcyo+UOPKBxMp0MSG4M8AEMTF1pVXlugCowaNB3CPN9l6+URVx9zRUy7v3DcoEh0w2yqHmQOc/mMvLDfS09mK6nRgLZSE2O6cDBEIJ0= <EMAIL>
      - ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABAQDKOr0O1JjjTkmeUiwLnKgJIzp0ZD3fjRpDSrHtNiwScUbgEayPxMeK9bZWkGmfTsL6WS841h+XUCby9X96ISogzFxeIa03fWNn4LpYD/Vj+UuscaXkHiEYuQniDStPYqwyYD6Ao8LJuacmJ0nmUMJsrRu8zBppzRqlQWH9D+u8VBSN+HB4/AbMp+Znb50Rr94S4ecX0a9o7cqfX+dXJAt9Pb/HBXtH8VMBvtrQIc3eSdz4xZU0QvvALb3VAJdYv4Xjtfyl4A0ubamA6HktqvXV4RIfBNqAL53msmhhYv/D2CB4zrqO7n1dOIU2gSzJrmoptCfjgJo/feTBdfCHbcR9 thankview-prod-default
    shell: /bin/bash

runcmd:
  - aws s3 cp s3://thankview-secrets/common/thankview_deploy /root/thankview_deploy
  - chmod 0600 /root/thankview_deploy
  - cp /root/thankview_deploy /home/<USER>/.ssh/thankview_deploy
  - chown ubuntu:ubuntu /home/<USER>/.ssh/thankview_deploy
  - chmod 400 /home/<USER>/.ssh/thankview_deploy
  - cd /var/www/thank-views
  - chown -R ubuntu:ubuntu /var/www
  - sudo -H -u ubuntu git checkout production
  - sudo -H -u ubuntu GIT_SSH_COMMAND='ssh -i /home/<USER>/.ssh/thankview_deploy -o StrictHostKeyChecking=no' git pull origin production
  - aws --region us-east-1 ssm get-parameter --name "/thankview-ps/prod/video-server" --with-decryption | jq -r .Parameter.Value > .env
  - find /var/www -type f -exec chmod 644 {} \;
  - find /var/www -type d -exec chmod 775 {} \;
  - chgrp -R ubuntu /var/www/thank-views/storage /var/www/thank-views/bootstrap /var/www/thank-views/vendor
  - chmod -R ug+rwx /var/www/thank-views/storage /var/www/thank-views/bootstrap /var/www/thank-views/vendor
  - sudo -H -u ubuntu composer install --no-dev
  - sudo -H -u ubuntu composer dump-autoload
  - sudo -H -u ubuntu composer update
  - sudo -H -u ubuntu php artisan clear-compiled
  - sudo -H -u ubuntu php artisan route:clear
  - sudo -H -u ubuntu php artisan view:clear
  - sudo -H -u ubuntu php artisan config:clear
  - sudo supervisorctl reread
  - sudo supervisorctl update
  - sudo supervisorctl restart laravel-worker:*
  - echo "25 01 * * * supervisorctl stop laravel-worker:*" >> /var/spool/cron/crontabs/root

  - >
    echo "Setting up hostname..." &&
    INSTANCE_ID=$$(curl -s http://***************/latest/meta-data/instance-id) &&
    echo "Instance ID: $$INSTANCE_ID" &&
    AZ=$$(curl -s http://***************/latest/meta-data/placement/availability-zone) &&
    REGION=$${AZ%?} &&
    echo "Region: $$REGION" &&
    NAME_TAG=$$(aws ec2 describe-tags --filters "Name=resource-id,Values=$$INSTANCE_ID" "Name=key,Values=Name" --region $$REGION --output text --query 'Tags[0].Value') &&
    echo "Name tag: $$NAME_TAG" &&
    PRIVATE_IP=$$(curl -s http://***************/latest/meta-data/local-ipv4) &&
    PRIVATE_IP_FORMATTED=$$(echo $$PRIVATE_IP | tr '.' '-') &&
    echo "Private IP formatted: $$PRIVATE_IP_FORMATTED" &&
    NEW_HOSTNAME="thankview-$$NAME_TAG-$$PRIVATE_IP_FORMATTED" &&
    echo "Setting hostname to: $$NEW_HOSTNAME" &&
    sudo hostnamectl set-hostname $$NEW_HOSTNAME &&
    echo "127.0.0.1 $$NEW_HOSTNAME" | sudo tee -a /etc/hosts &&
    echo "Hostname setup complete"

  - >
    cd /tmp &&
    DD_API_KEY=${datadog_api_key}
    DD_SITE="datadoghq.com"
    DD_APM_INSTRUMENTATION_ENABLED=host
    DD_ENV=prod
    bash -c "$(curl -L https://s3.amazonaws.com/dd-agent/scripts/install_script_agent7.sh)"
  - >
    cd /tmp &&
    curl -LO https://github.com/DataDog/dd-trace-php/releases/latest/download/datadog-setup.php
    php datadog-setup.php --php-bin=all
  - >
    until [ -f /etc/datadog-agent/datadog.yaml ]; do sleep 5; done
  - >
    echo "process_config:
      process_collection:
        enabled: true
    apm_config:
      enabled: true" >> /etc/datadog-agent/datadog.yaml
  - sudo systemctl restart datadog-agent

write_files:
  - encoding: b64
    content: ${digicert_ca_encoded}
    owner: root:root
    path: /etc/ssl/DigiCertCA.crt
    permissions: '0644'

  - encoding: b64
    content: ${elasticsearch_ca_encoded}
    owner: root:root
    path: /etc/ssl/elasticsearch-ca.pem
    permissions: '0644'

  - encoding: b64
    content: ${video_worker_supervisor_conf_encoded}
    owner: root:root
    path: /etc/supervisor/conf.d/video-worker.conf
    permissions: '0644'
  
  - owner: ubuntu:crontab
    path: /var/spool/cron/crontabs/ubuntu
    permissions: '0600'
    content: |
      * * * * * if [ -f "/var/www/thank-views/.env" ]; then php /var/www/thank-views/artisan schedule:run >> /dev/null 2>&1; fi

